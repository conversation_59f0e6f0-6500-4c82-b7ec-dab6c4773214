/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://www.aistak.com',
  generateRobotsTxt: true,
  robotsTxtOptions: {
    policies: [
      { userAgent: '*', allow: '/' },
    ],
  },
  // 支持国际化的站点地图
  alternateRefs: [
    {
      href: (process.env.SITE_URL || 'https://www.aistak.com') + '/en',
      hreflang: 'en',
    },
    {
      href: (process.env.SITE_URL || 'https://www.aistak.com') + '/zh',
      hreflang: 'zh',
    },
  ],
  // 排除一些路径
  exclude: [
    '/404',
    '/500',
    '/auth/signin',
    '/api/*',
    '/_next/*',
  ],
  // 手动添加额外路径用于测试
  additionalPaths: async (config) => {
    const result = [];
    
    // 添加一些常见的路径用于测试
    const paths = [
      { loc: '/' },
      { loc: '/en' },
      { loc: '/zh' },
      { loc: '/en/blog' },
      { loc: '/zh/blog' },
      { loc: '/en/about' },
      { loc: '/zh/about' },
    ];
    
    for (const path of paths) {
      result.push(path);
    }
    
    return result;
  },
}; 