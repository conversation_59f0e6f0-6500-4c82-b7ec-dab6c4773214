import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from '@/i18n/routing';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import '../globals.css';
import Header from '@/components/Header';
// import Footer from '@/components/Footer';
import { getLandingPage } from '@/app/actions';
import { Providers } from '@/app/providers';
import { SplashCursor } from "@/components/ui/splash-cursor"
import { Footer } from '@/components/ui/footer-section';
import Script from 'next/script';
import type { Metadata } from 'next';

// 定义全局元数据 (使用默认英文文本)
export const metadata: Metadata = {
  title: 'Aistak - Your AI Tools Navigator',
  description: 'Discover the best AI tools and software. Aistak helps you find AI solutions for productivity, creativity, and more.',
  keywords: ['AI tools', 'artificial intelligence', 'software', 'productivity', 'Aistak', 'AI directory', 'best AI tools'],
  // 可以添加其他希望全局应用的元数据，例如：
  // applicationName: 'Aistak',
  // authors: [{ name: 'Aistak Team', url: 'https://www.aistak.com' }], // 请替换为您的URL
  // creator: 'Aistak Team',
  // publisher: 'Aistak',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  // themeColor: [
  //   { media: '(prefers-color-scheme: light)', color: 'white' },
  //   { media: '(prefers-color-scheme: dark)', color: 'black' },
  // ],
  // manifest: '/manifest.json', // 如果您有 manifest 文件
  // icons: { // 网站图标
  //   icon: '/favicon.ico',
  //   shortcut: '/favicon-16x16.png',
  //   apple: '/apple-touch-icon.png',
  // },
  // metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aistak.com'), // 建议在 page.tsx 中具体设置，以确保 canonical URLs 正确
};

// 修改类型定义，使用 generateStaticParams 来处理参数
export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

// 更新 Layout 组件以支持异步 params
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;
  
  // 验证 locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }
 
  // 并行获取消息和页面数据
  const [messages, page] = await Promise.all([
    getMessages(locale),
    getLandingPage(locale)
  ]);

  return (
    <html lang={locale} className="scroll-smooth" suppressHydrationWarning>
      <head>
        {/* Google tag (gtag.js) */}
        <Script 
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-9ST4TEHF2S"
        />
        <Script 
          id="google-analytics-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-9ST4TEHF2S');
            `,
          }}
        />
      </head>
      <body>
          <Script
            src="https://umami.wenhaofree.com/script.js"
            data-website-id="8beda2bf-5112-490a-b6ba-22dfdc75a47f"
            defer
          />
          <SplashCursor />
          <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers>
            <div className="fixed inset-x-0 top-0 z-50 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="h-full">
                  {page.header && <Header header={page.header} />}
                </div>
            </div>
            <main className="flex-1 pt-16">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </main>
            {/* <div className="border-t">
              {page.footer && <Footer footer={page.footer} />}
            </div> */}
            <Footer />
            </Providers>
          </NextIntlClientProvider>
        
      </body>
    </html>
  );
}
