## 排期：
### TODO：第二期
1. 中文分类

### TODO：第一期
1. 详情页：
  - 描述文本
  - 流量统计
2. 加载速度：
  - 首页内容：
  - 下来列表查询内容
3. 分类页面内容：

4. 页面上： 报错问题
  - 404 页面
  - 500页面处理

5. 登陆功能，注册功能，
6. 分类标签查询
7. 

# AISTAK

## 项目简介

AISTAK是一个基于Next.js构建的全栈Web应用，提供了AI工具目录、用户认证和订阅管理功能。该项目采用微服务架构，前端使用Next.js，后端分离为Django开发的工具数据服务。

## 核心功能

- 🔑 **身份认证**: 支持Google和GitHub OAuth登录
- 💳 **支付系统**: 整合Stripe处理订阅和一次性支付
- 🌎 **国际化**: 支持多语言界面
- 🔍 **工具目录**: 展示和搜索AI工具
- 👤 **用户管理**: 基于积分的访问控制

## 技术栈

### 前端
- **框架**: Next.js
- **状态管理**: React Hooks
- **数据库ORM**: Prisma
- **样式**: Tailwind CSS
- **认证**: NextAuth.js
- **支付**: Stripe

### 后端服务
- **API服务**: Django REST Framework
- **数据库**: PostgreSQL

## 环境要求

- Node.js 16.x 或更高版本
- PostgreSQL 12.x 或更高版本
- Python 3.8+ (用于后端服务)

## 项目设置

### 1. 克隆项目

```bash
git clone https://github.com/yourusername/aistak.git
cd aistak
```

### 2. 安装依赖

```bash
npm install
# 或者使用yarn
yarn install
```

### 3. 配置环境变量

创建`.env.local`文件并设置以下变量：

```
DATABASE_URL="postgresql://username:password@localhost:5432/aistak"
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_ID=your-github-id
GITHUB_SECRET=your-github-secret

# Stripe
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
```

### 4. 数据库设置

#### PostgreSQL数据库创建

```sql
-- 创建数据库
CREATE DATABASE aistak;

-- 创建用户并设置密码
CREATE USER aistak_user WITH ENCRYPTED PASSWORD 'your_password';

-- 赋予权限
GRANT ALL PRIVILEGES ON DATABASE aistak TO aistak_user;
```

#### 使用Prisma初始化数据库

```bash
# 生成Prisma客户端
npx prisma generate

# 应用数据库迁移
npx prisma migrate dev --name init
```

#### 工具目录数据库表创建

```sql
-- 分类表
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  slug VARCHAR(50) UNIQUE NOT NULL,
  icon_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 分类翻译表
CREATE TABLE category_translations (
  id SERIAL PRIMARY KEY,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  locale VARCHAR(10) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(category_id, locale)
);

-- 标签表
CREATE TABLE tags (
  id SERIAL PRIMARY KEY,
  slug VARCHAR(50) UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 标签翻译表
CREATE TABLE tag_translations (
  id SERIAL PRIMARY KEY,
  tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
  locale VARCHAR(10) NOT NULL,
  name VARCHAR(50) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tag_id, locale)
);

-- 工具表
CREATE TABLE tools (
  id SERIAL PRIMARY KEY,
  tool_id VARCHAR(50) UNIQUE NOT NULL,
  icon_url TEXT NOT NULL,
  url TEXT NOT NULL,
  pricing_type VARCHAR(20) NOT NULL,
  is_premium BOOLEAN DEFAULT FALSE,
  is_new BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  rating DECIMAL(2,1),
  api_available BOOLEAN DEFAULT FALSE,
  publisher VARCHAR(100),
  publisher_url TEXT,
  terms_url TEXT,
  privacy_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 工具翻译表
CREATE TABLE tool_translations (
  id SERIAL PRIMARY KEY,
  tool_id VARCHAR(50) REFERENCES tools(tool_id) ON DELETE CASCADE,
  locale VARCHAR(10) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT NOT NULL,
  long_description TEXT,
  usage_instructions TEXT,
  pricing_details TEXT,
  integration_info TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tool_id, locale)
);

-- 工具功能表
CREATE TABLE tool_features (
  id SERIAL PRIMARY KEY,
  tool_id VARCHAR(50) REFERENCES tools(tool_id) ON DELETE CASCADE,
  locale VARCHAR(10) NOT NULL,
  feature VARCHAR(100) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(tool_id, locale, feature)
);

-- 工具-分类关联表
CREATE TABLE tool_categories (
  tool_id VARCHAR(50) REFERENCES tools(tool_id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (tool_id, category_id)
);

-- 工具-标签关联表
CREATE TABLE tool_tags (
  tool_id VARCHAR(50) REFERENCES tools(tool_id) ON DELETE CASCADE,
  tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (tool_id, tag_id)
);

-- 创建索引
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_category_translations_locale ON category_translations(locale);
CREATE INDEX idx_category_translations_category_locale ON category_translations(category_id, locale);
CREATE INDEX idx_tags_slug ON tags(slug);
CREATE INDEX idx_tag_translations_locale ON tag_translations(locale);
CREATE INDEX idx_tag_translations_tag_locale ON tag_translations(tag_id, locale);
CREATE INDEX idx_tools_tool_id ON tools(tool_id);
CREATE INDEX idx_tools_is_premium ON tools(is_premium);
CREATE INDEX idx_tools_is_new ON tools(is_new);
CREATE INDEX idx_tools_is_featured ON tools(is_featured);
CREATE INDEX idx_tool_translations_locale ON tool_translations(locale);
CREATE INDEX idx_tool_translations_tool_locale ON tool_translations(tool_id, locale);
CREATE INDEX idx_tool_features_tool_locale ON tool_features(tool_id, locale);
CREATE INDEX idx_tool_categories_tool ON tool_categories(tool_id);
CREATE INDEX idx_tool_categories_category ON tool_categories(category_id);
CREATE INDEX idx_tool_tags_tool ON tool_tags(tool_id);
CREATE INDEX idx_tool_tags_tag ON tool_tags(tag_id);
```

### 5. 运行开发服务器

```bash
npm run dev
# 或者使用yarn
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 6. 运行工具目录后端服务

```bash
# 切换到后端目录
cd aistak_django

# 安装Python依赖
pip install -r requirements.txt

# 运行迁移
python manage.py migrate

# 创建管理员账户
python manage.py createsuperuser

# 运行服务
python manage.py runserver
```

后端 API 将在 [http://localhost:8000](http://localhost:8000) 上可用。

## 构建与部署

### 构建生产版本

```bash
npm run build
# 或者使用yarn
yarn build
```

### 启动生产服务器

```bash
npm start
# 或者使用yarn
yarn start
```

## API文档

完整的API文档可在[Django后端的README中](./aistak_django/readme.md)找到。

## 项目结构

```
aistak/
├── .env.local           # 环境变量文件
├── components/          # React组件
├── lib/                 # 工具函数和库
├── pages/               # Next.js页面
├── prisma/              # Prisma ORM配置
│   └── schema.prisma    # 数据库模型定义
├── public/              # 静态资源
├── styles/              # CSS样式文件
├── aistak_django/       # Django后端服务
├── package.json         # 项目配置和依赖
└── README.md            # 项目文档
```

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 联系方式

- 项目作者：WenHaoFree
- 电子邮箱：<EMAIL>
- GitHub：[https://github.com/wenhaofree](https://github.com/wenhaofree)
